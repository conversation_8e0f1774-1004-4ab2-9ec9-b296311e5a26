import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/models/streak/streak.model.dart';

/// Mock database service for testing
/// Provides in-memory storage for tasks and streaks without actual database operations
class MockDatabaseService {
  static final MockDatabaseService instance = MockDatabaseService._instance();
  MockDatabaseService._instance();

  // In-memory storage
  final List<Task> _tasks = [];
  final List<StreakModel> _streaks = [];
  final Map<String, dynamic> _streakData = {};

  // Reset all data - useful for test cleanup
  void reset() {
    _tasks.clear();
    _streaks.clear();
    _streakData.clear();
  }

  // Task operations
  Future<void> insertTask(Task task) async {
    await Future.delayed(
        Duration(milliseconds: 10)); // Simulate async operation
    _tasks.add(task);
  }

  Future<List<Task>> getTasks() async {
    await Future.delayed(Duration(milliseconds: 10));
    return _tasks.where((task) => !task.isCompleted).toList();
  }

  Future<List<Task>> getCompletedTasks() async {
    await Future.delayed(Duration(milliseconds: 10));
    return _tasks.where((task) => task.isCompleted).toList();
  }

  Future<void> updateTask(Task task) async {
    await Future.delayed(Duration(milliseconds: 10));
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
    }
  }

  Future<void> deleteTask(String taskId) async {
    await Future.delayed(Duration(milliseconds: 10));
    _tasks.removeWhere((task) => task.id == taskId);
  }

  Future<void> deleteAllTasks() async {
    await Future.delayed(Duration(milliseconds: 10));
    _tasks.clear();
  }

  // Streak operations
  Future<void> insertOrUpdateStreakData(Map<String, dynamic> streakData) async {
    await Future.delayed(Duration(milliseconds: 10));
    final date = streakData['date'] as String;
    _streakData[date] = streakData;
  }

  Future<List<Map<String, dynamic>>> getStreakHistory() async {
    await Future.delayed(Duration(milliseconds: 10));
    return _streakData.values.cast<Map<String, dynamic>>().toList();
  }

  Future<void> deleteStreakData() async {
    await Future.delayed(Duration(milliseconds: 10));
    _streakData.clear();
  }

  Future<void> deleteStreakDataForDate(String date) async {
    await Future.delayed(Duration(milliseconds: 10));
    _streakData.remove(date);
  }

  Future<int> getCurrentStreakFromDB() async {
    await Future.delayed(Duration(milliseconds: 10));
    if (_streakData.isEmpty) return 0;

    final sortedEntries = _streakData.entries.toList()
      ..sort((a, b) => b.key.compareTo(a.key));

    return sortedEntries.first.value['currentStreak'] as int? ?? 0;
  }

  Future<int> getLongestStreakFromDB() async {
    await Future.delayed(Duration(milliseconds: 10));
    if (_streakData.isEmpty) return 0;

    int maxStreak = 0;
    for (final entry in _streakData.values) {
      final streak = entry['longestStreak'] as int? ?? 0;
      if (streak > maxStreak) maxStreak = streak;
    }
    return maxStreak;
  }

  Future<int> getTotalCompletedDaysFromDB() async {
    await Future.delayed(Duration(milliseconds: 10));
    return _streakData.values
        .where((data) => data['allTasksCompleted'] == 1)
        .length;
  }

  Future<void> updateStreakCounters(
      int currentStreak, int longestStreak) async {
    await Future.delayed(Duration(milliseconds: 10));
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_streakData.containsKey(today)) {
      _streakData[today]!['currentStreak'] = currentStreak;
      _streakData[today]!['longestStreak'] = longestStreak;
    }
  }

  // Helper methods for testing
  List<Task> getAllTasks() => List.from(_tasks);
  Map<String, dynamic> getAllStreakData() => Map.from(_streakData);

  void addTestTask(Task task) => _tasks.add(task);
  void addTestStreakData(String date, Map<String, dynamic> data) =>
      _streakData[date] = data;
}
