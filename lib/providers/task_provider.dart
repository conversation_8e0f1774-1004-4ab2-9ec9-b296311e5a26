import 'package:flutter/foundation.dart';
import 'package:unstack/logic/tasks/task_impl.dart';
import 'package:unstack/logic/streak/streak_impl.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/providers/task_provider_contract.dart';
import 'package:unstack/utils/app_logger.dart';

enum TaskState {
  initial,
  loading,
  loaded,
  error,
}

class TaskProvider extends ChangeNotifier implements ITaskProviderContract {
  final TaskManager _taskManager = TaskManager();
  final StreakManager _streakManager = StreakManager();

  List<Task> _remainingTasks = [];
  List<Task> _completedTasks = [];
  List<Task> _pendingTasks = [];

  @override
  List<Task> get remainingTasks => _remainingTasks;
  @override
  List<Task> get pendingTasks => _pendingTasks;
  @override
  List<Task> get allTasks =>
      [...remainingTasks, ...completedTasks, ...pendingTasks];

  List<Task> get todayTasks => [...remainingTasks, ...completedTasks];
  @override
  List<Task> get completedTasks => _completedTasks;

  // Statistics
  int get totalTasksCount => [...remainingTasks, ...completedTasks].length;
  int get completedTasksCount => completedTasks.length;

  bool get todaysTasksCompleted =>
      todayTasks.isNotEmpty && todayTasks.every((task) => task.isCompleted);

  TaskProvider() {
    loadTasks();
    loadCompletedTasks();
    loadPendingTasks();
    // Temporary: Reset all task priorities to fix existing data
    resetAllTaskPriorities();
  }

  /// Get task by id
  @override
  Task? getTaskById(String taskId) {
    try {
      return allTasks.firstWhere(
        (task) => task.id == taskId,
      );
    } catch (e) {
      AppLogger.warning('Task not found with id: $taskId');
      return null;
    }
  }

  /// Load all tasks from database
  @override
  Future<void> loadTasks() async {
    try {
      final tasks = await _taskManager.getRemainingTasks();
      _remainingTasks = tasks;

      // Debug logging
      AppLogger.info('Loaded ${tasks.length} tasks from database:');
      for (var t in _remainingTasks) {
        AppLogger.info(
            '  - ${t.title}: priority=${t.priority.name}, priorityIndex=${t.priorityIndex}');
      }

      notifyListeners();
    } catch (e) {
      AppLogger.error('Error loading tasks: $e');
      _setError('Failed to load tasks: ${e.toString()}');
    }
  }

  /// Load completed tasks from database
  @override
  Future<void> loadCompletedTasks() async {
    try {
      final completedTasks = await _taskManager.getCompletedTasks();
      _completedTasks = completedTasks;
      notifyListeners();
      AppLogger.info('Loaded ${completedTasks.length} completed tasks');
    } catch (e) {
      AppLogger.error('Error loading completed tasks: $e');
      _setError('Failed to load completed tasks: ${e.toString()}');
    }
  }

  @override
  Future<void> loadPendingTasks() async {
    try {
      final pendingTasks = await _taskManager.getPendingTasks();
      _pendingTasks = pendingTasks;
      notifyListeners();
      AppLogger.info('Loaded ${pendingTasks.length} pending tasks');
    } catch (e) {
      AppLogger.error('Error loading pending tasks: $e');
      _setError('Failed to load pending tasks: ${e.toString()}');
    }
  }

  /// Add a new task
  @override
  Future<bool> addTask(Task task) async {
    try {
      await _taskManager.addTask(task);
      _remainingTasks.add(task);
      _remainingTasks.sort((a, b) {
        if (a.priorityIndex == b.priorityIndex) return 0;
        return a.priorityIndex.compareTo(b.priorityIndex);
      });

      // Debug logging
      AppLogger.info(
          'Added task: ${task.title} with priority: ${task.priority.name} and priorityIndex: ${task.priorityIndex}');
      AppLogger.info('Current tasks order:');
      for (var t in _remainingTasks) {
        AppLogger.info(
            '  - ${t.title}: priority=${t.priority.name}, priorityIndex=${t.priorityIndex}');
      }

      notifyListeners();

      // Adding a new task removes any existing streak for that date
      await _streakManager.removeStreakForDate(task.createdAt);

      return true;
    } catch (e) {
      AppLogger.error('Error adding task: $e');
      _setError('Failed to add task: ${e.toString()}');
      return false;
    }
  }

  /// Update an existing task
  @override
  Future<bool> updateTask(Task updatedTask) async {
    try {
      await _taskManager.updateTask(updatedTask);
      bool taskFound = false;
      // Check in today's tasks
      final todayIndex =
          _remainingTasks.indexWhere((task) => task.id == updatedTask.id);
      if (todayIndex != -1) {
        _remainingTasks[todayIndex] = updatedTask;
        taskFound = true;
      }

      // Check in pending tasks
      final pendingIndex =
          _pendingTasks.indexWhere((task) => task.id == updatedTask.id);
      if (pendingIndex != -1) {
        _pendingTasks[pendingIndex] = updatedTask;
        taskFound = true;
      }

      // Check in completed tasks
      final completedIndex =
          _completedTasks.indexWhere((task) => task.id == updatedTask.id);
      if (completedIndex != -1) {
        _completedTasks[completedIndex] = updatedTask;
        taskFound = true;
      }
      notifyListeners();

      if (taskFound) {
        AppLogger.info('Updated task: ${updatedTask.title}');
        return true;
      } else {
        AppLogger.warning('Task not found for update: ${updatedTask.id}');
        return false;
      }
    } catch (e) {
      AppLogger.error('Error updating task: $e');
      _setError('Failed to update task: ${e.toString()}');
      return false;
    }
  }

  @override
  Future<bool> postponeTask(Task task) async {
    try {
      final postponedTask = task.copyWith(createdAt: DateTime.now());
      await _taskManager.updateTask(postponedTask);

      // Remove from pending tasks list
      _pendingTasks.removeWhere((t) => t.id == task.id);

      // Add the updated task (with new createdAt) to today's tasks
      _remainingTasks.add(postponedTask);
      // Adding a new task removes any existing streak for that date
      await _streakManager.removeStreakForDate(task.createdAt);

      notifyListeners();

      AppLogger.info('Postponed task: ${task.title} to today');
      return true;
    } catch (e) {
      AppLogger.error('Error postponing task: $e');
      _setError('Failed to postpone task: ${e.toString()}');
      return false;
    }
  }

  /// Delete a task
  @override
  Future<bool> deleteTask(String taskId) async {
    try {
      await _taskManager.deleteTask(taskId);
      _remainingTasks.removeWhere((task) => task.id == taskId);
      _completedTasks.removeWhere((task) => task.id == taskId);
      _pendingTasks.removeWhere((task) => task.id == taskId);

      notifyListeners();

      AppLogger.info('Deleted task: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting task: $e');
      _setError('Failed to delete task: ${e.toString()}');
      return false;
    }
  }

  /// Mark task as completed
  @override
  Future<bool> markTaskAsCompleted(Task task) async {
    try {
      final completedTask =
          task.copyWith(isCompleted: true, completedAt: DateTime.now());
      await _taskManager.markTaskAsCompleted(completedTask);
      _remainingTasks.removeWhere((t) => t.id == task.id);
      _completedTasks.add(completedTask);
      notifyListeners();

      AppLogger.info(
          'Task completed: ${task.title}, created on: ${task.createdAt.toIso8601String().split('T')[0]}');
      AppLogger.info('Today\'s tasks completed: $todaysTasksCompleted');

      return true;
    } catch (e) {
      AppLogger.error('Error marking task as completed: $e');
      _setError('Failed to mark task as completed: ${e.toString()}');
      return false;
    }
  }

  /// Mark task as incomplete
  @override
  Future<bool> markTaskAsIncomplete(Task task) async {
    try {
      final incompleteTask =
          task.copyWith(isCompleted: false, completedAt: null);
      await _taskManager.markTaskAsIncomplete(incompleteTask);
      _completedTasks.removeWhere((t) => t.id == task.id);
      _remainingTasks.add(incompleteTask);
      notifyListeners();

      return true;
    } catch (e) {
      AppLogger.error('Error marking task as incomplete: $e');
      _setError('Failed to mark task as incomplete: ${e.toString()}');
      return false;
    }
  }

  /// Delete all tasks
  @override
  Future<bool> deleteAllTasks() async {
    try {
      await _taskManager.deleteAllTasks();
      _remainingTasks.clear();
      notifyListeners();
      AppLogger.info('Deleted all tasks');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting all tasks: $e');
      _setError('Failed to delete all tasks: ${e.toString()}');
      return false;
    }
  }

  /// Update the in-memory tasks order immediately (for smooth UI during reordering)
  void updateTasksOrder(List<Task> reorderedTasks) {
    _remainingTasks = reorderedTasks;
    notifyListeners();
  }

  /// Reset all task priorities to ensure consistency (call this once to fix existing data)
  Future<void> resetAllTaskPriorities() async {
    try {
      final priorityIndexMap = {
        'low': 3,
        'medium': 2,
        'high': 1,
        'urgent': 0,
      };

      // Load all tasks and reset their priorityIndex based on their priority
      await loadTasks();
      for (var task in _remainingTasks) {
        final newPriorityIndex = priorityIndexMap[task.priority.name] ?? 0;
        if (task.priorityIndex != newPriorityIndex) {
          final updatedTask = task.copyWith(priorityIndex: newPriorityIndex);
          await _taskManager.updateTask(updatedTask);
          AppLogger.info(
              'Reset ${task.title}: priority=${task.priority.name}, priorityIndex=${task.priorityIndex} -> $newPriorityIndex');
        }
      }

      // Reload tasks to get the updated order
      await loadTasks();
      AppLogger.info('All task priorities have been reset');
    } catch (e) {
      AppLogger.error('Error resetting task priorities: $e');
    }
  }

  /// Refresh tasks from database
  @override
  Future<void> refreshTasks() async {
    await loadTasks();
  }

  void _setError(String message) {
    notifyListeners();
  }

  /// Update streak data for a specific date based on tasks for that date
  // Future<void> _updateStreakForTaskDate(DateTime date) async {
  //   try {
  //     // Get all tasks for the specific date
  //     final tasksForDate = getTasksForDate(date);
  //     final completedTasksForDate = getCompletedTasksForDate(date);
  //     final allTasksCompleted = areAllTasksCompletedForDate(date);

  //     AppLogger.info(
  //         'Updating streak for date: ${date.toIso8601String().split('T')[0]} '
  //         'with ${tasksForDate.length} tasks, ${completedTasksForDate.length} completed, allCompleted: $allTasksCompleted');

  //     // Update streak data for this date
  //     await _streakManager.updateDayCompletionForDate(
  //       date,
  //       tasksForDate.length,
  //       completedTasksForDate.length,
  //       allTasksCompleted,
  //     );

  //     AppLogger.info(
  //         'Streak update completed for date: ${date.toIso8601String().split('T')[0]}');
  //   } catch (e) {
  //     AppLogger.error('Error updating streak for date: $e');
  //     // Don't rethrow to avoid breaking task operations
  //   }
  // }
}
